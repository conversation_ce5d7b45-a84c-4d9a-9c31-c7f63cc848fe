{"name": "cf-admin-panel-structure-v1", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "dev:server": "tsx watch app/server/index.ts", "start": "node ./build/server/index.js", "start:dev": "tsx app/server/index.ts", "typecheck": "react-router typegen && tsc", "lint": "eslint app --ext .ts,.tsx", "format": "prettier --write app"}, "dependencies": {"@react-router/node": "^7.7.0", "@react-router/serve": "^7.7.0", "antd": "^5.26.6", "axios": "^1.10.0", "compression": "^1.8.1", "cors": "^2.8.5", "express": "^4.21.2", "helmet": "^7.2.0", "http-proxy-middleware": "^2.0.9", "isbot": "^5.1.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.0"}, "devDependencies": {"@react-router/dev": "^7.7.0", "@tailwindcss/vite": "^4.1.4", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/helmet": "^0.0.48", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "tsx": "^4.20.3", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}