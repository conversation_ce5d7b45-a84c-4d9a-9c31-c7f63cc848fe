# CF Admin Panel Structure v1

A dynamic, config-driven admin panel with proxy backend capabilities built with React Router v7, Express.js, and Ant Design.

## Features

- **Dynamic Routing**: Config-driven route system that allows changing paths dynamically (e.g., `/admin/` → `/asewenermHFWs/`)
- **Proxy Backend**: Express.js middleware for API proxying with support for all HTTP methods
- **Flat Route Structure**: Uses React Router v7's flat route structure for optimal performance
- **Nested Layouts**: Hierarchical layout system with admin layouts and reusable components
- **Scalable Architecture**: Designed to handle 1000+ routes and API endpoints
- **Mock API Support**: Built-in mock responses for development
- **Rate Limiting**: Configurable rate limiting for API endpoints
- **Type Safety**: Full TypeScript support throughout the application

## Quick Start

### Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Start development server with custom proxy backend
npm run start:dev
```

### Production

```bash
# Build the application
npm run build

# Start production server
npm start
```

## Configuration

### Route Configuration

Edit `app/config/routes.config.ts` to modify routes, API endpoints, and proxy settings:

```typescript
// Change admin route path
routeMappings: [
  {
    original: "/admin",
    mapped: "/asewenermHFWs", // Obfuscated admin path
    active: true,
  },
];
```

### API Routes

Configure API endpoints with proxy settings:

```typescript
apiRoutes: [
  {
    path: "/api/users",
    methods: ["GET", "POST"],
    proxy: true,
    target: "http://localhost:3001",
  },
];
```

### Page Routes

Define page routes with layouts and metadata:

```typescript
pageRoutes: [
  {
    path: "/admin/users",
    component: "admin/users/layout.tsx",
    layout: "admin",
    protected: true,
    children: [...]
  }
]
```

## Architecture

### Directory Structure

```
app/
├── config/
│   └── routes.config.ts      # Route and API configuration
├── routes/
│   ├── admin/               # Admin panel routes
│   │   ├── layout.tsx       # Admin layout
│   │   ├── dashboard.tsx    # Dashboard page
│   │   ├── users/           # User management
│   │   ├── settings/        # Settings pages
│   │   └── reports/         # Reports pages
│   └── home.tsx             # Home page
├── server/
│   ├── index.ts             # Express server with proxy
│   └── proxy-middleware.ts  # Proxy middleware
├── utils/
│   ├── api-client.ts        # Centralized API client
│   └── route-generator.ts   # Dynamic route generation
└── routes.ts                # React Router configuration
```

### Key Components

1. **Configuration System** (`app/config/routes.config.ts`)

   - Centralized configuration for routes, APIs, and proxy settings
   - Dynamic path mapping for security obfuscation
   - Support for nested routes and layouts

2. **Proxy Middleware** (`app/server/proxy-middleware.ts`)

   - Express.js middleware for API proxying
   - Rate limiting and error handling
   - Mock response support for development

3. **API Client** (`app/utils/api-client.ts`)

   - Centralized HTTP client with interceptors
   - Automatic authentication handling
   - Request/response logging and error handling

4. **Route Generator** (`app/utils/route-generator.ts`)
   - Dynamic route generation from configuration
   - Breadcrumb generation
   - Navigation menu structure

## API Endpoints

### Built-in Endpoints

- `GET /api/health` - Health check
- `GET /api/docs` - API documentation
- `GET /api/dashboard/stats` - Dashboard statistics (mock)

### User Management

- `GET /api/users` - List users
- `POST /api/users` - Create user
- `GET /api/users/:id` - Get user details
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Settings

- `GET /api/settings` - Get settings
- `PUT /api/settings` - Update settings

## Customization

### Adding New Routes

1. Update `app/config/routes.config.ts`:

```typescript
pageRoutes: [
  {
    path: "/admin/new-section",
    component: "admin/new-section/layout.tsx",
    layout: "admin",
    protected: true,
  },
];
```

2. Create the component file:

```typescript
// app/routes/admin/new-section/layout.tsx
export default function NewSectionLayout() {
  return <div>New Section</div>;
}
```

3. Update `app/routes.ts` to include the new route.

### Adding API Endpoints

1. Configure in `app/config/routes.config.ts`:

```typescript
apiRoutes: [
  {
    path: "/api/new-endpoint",
    methods: ["GET", "POST"],
    proxy: true,
    target: "http://localhost:3001",
  },
];
```

2. The proxy middleware will automatically handle the routing.

### Changing Route Paths

Simply update the `routeMappings` in the configuration:

```typescript
routeMappings: [
  {
    original: "/admin",
    mapped: "/secure-admin-xyz", // New obfuscated path
    active: true,
  },
];
```

All internal links will automatically use the mapped paths.

## Security Features

- Route obfuscation through dynamic path mapping
- Rate limiting on API endpoints
- CORS configuration
- Helmet security headers
- Authentication token management

## Development Tools

- TypeScript for type safety
- ESLint for code quality
- Prettier for code formatting
- Hot reload in development
- Comprehensive error handling

## License

MIT

## Features

- 🚀 Server-side rendering
- ⚡️ Hot Module Replacement (HMR)
- 📦 Asset bundling and optimization
- 🔄 Data loading and mutations
- 🔒 TypeScript by default
- 🎉 TailwindCSS for styling
- 📖 [React Router docs](https://reactrouter.com/)

## Getting Started

### Installation

Install the dependencies:

```bash
npm install
```

### Development

Start the development server with HMR:

```bash
npm run dev
```

Your application will be available at `http://localhost:5173`.

## Building for Production

Create a production build:

```bash
npm run build
```

## Deployment

### Docker Deployment

To build and run using Docker:

```bash
docker build -t my-app .

# Run the container
docker run -p 3000:3000 my-app
```

The containerized application can be deployed to any platform that supports Docker, including:

- AWS ECS
- Google Cloud Run
- Azure Container Apps
- Digital Ocean App Platform
- Fly.io
- Railway

### DIY Deployment

If you're familiar with deploying Node applications, the built-in app server is production-ready.

Make sure to deploy the output of `npm run build`

```
├── package.json
├── package-lock.json (or pnpm-lock.yaml, or bun.lockb)
├── build/
│   ├── client/    # Static assets
│   └── server/    # Server-side code
```

## Styling

This template comes with [Tailwind CSS](https://tailwindcss.com/) already configured for a simple default starting experience. You can use whatever CSS framework you prefer.

---

Built with ❤️ using React Router.
