import type { Config } from "@react-router/dev/config";

export default {
  // Server-side render by default, to enable SPA mode set this to `false`
  ssr: true,

  // Custom server configuration for proxy integration
  serverBuildFile: "server/index.js",

  // Build configuration
  buildDirectory: "build",

  // Development server configuration
  dev: {
    port: 3000,
  },

  // Vite configuration for development
  vite: {
    server: {
      port: 3000,
      host: true,
    },
    build: {
      target: "esnext",
    },
  },
} satisfies Config;
