import { createProxyMiddleware, type Options } from 'http-proxy-middleware';
import type { Request, Response, NextFunction } from 'express';
import { appConfig, getMappedPath, getOriginalPath, type ApiRouteConfig } from '../config/routes.config';

/**
 * Proxy middleware for handling API requests
 * Supports dynamic routing, rate limiting, and mock responses
 */

interface ProxyRequest extends Request {
  rateLimit?: {
    limit: number;
    current: number;
    remaining: number;
    resetTime: Date;
  };
}

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Rate limiting middleware
 */
function rateLimitMiddleware(config: ApiRouteConfig) {
  return (req: ProxyRequest, res: Response, next: NextFunction) => {
    if (!config.rateLimit) {
      return next();
    }

    const key = `${req.ip}:${req.path}`;
    const now = Date.now();
    const windowMs = config.rateLimit.windowMs;
    const maxRequests = config.rateLimit.max;

    let record = rateLimitStore.get(key);
    
    if (!record || now > record.resetTime) {
      record = {
        count: 0,
        resetTime: now + windowMs
      };
    }

    record.count++;
    rateLimitStore.set(key, record);

    const remaining = Math.max(0, maxRequests - record.count);
    const resetTime = new Date(record.resetTime);

    // Add rate limit info to request
    req.rateLimit = {
      limit: maxRequests,
      current: record.count,
      remaining,
      resetTime
    };

    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': maxRequests.toString(),
      'X-RateLimit-Remaining': remaining.toString(),
      'X-RateLimit-Reset': resetTime.toISOString()
    });

    if (record.count > maxRequests) {
      return res.status(429).json({
        error: 'Too Many Requests',
        message: `Rate limit exceeded. Try again after ${resetTime.toISOString()}`,
        retryAfter: Math.ceil((record.resetTime - now) / 1000)
      });
    }

    next();
  };
}

/**
 * Mock response middleware
 */
function mockResponseMiddleware(config: ApiRouteConfig) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!config.mockResponse || config.proxy) {
      return next();
    }

    // Simulate network delay
    const delay = Math.random() * 500 + 100; // 100-600ms delay
    
    setTimeout(() => {
      res.json(config.mockResponse);
    }, delay);
  };
}

/**
 * Create proxy middleware for a specific API route
 */
function createApiProxyMiddleware(config: ApiRouteConfig): any {
  if (!config.proxy || !config.target) {
    return mockResponseMiddleware(config);
  }

  const proxyOptions: Options = {
    target: config.target,
    changeOrigin: appConfig.proxy.changeOrigin,
    timeout: appConfig.proxy.timeout,
    pathRewrite: (path: string) => {
      // Convert mapped path back to original path for backend
      return getOriginalPath(path);
    },
    onError: (err: Error, req: Request, res: Response) => {
      console.error('Proxy error:', err.message);
      res.status(502).json({
        error: 'Bad Gateway',
        message: 'Failed to proxy request to backend service',
        timestamp: new Date().toISOString()
      });
    },
    onProxyReq: (proxyReq, req: Request) => {
      // Add custom headers
      proxyReq.setHeader('X-Forwarded-For', req.ip);
      proxyReq.setHeader('X-Forwarded-Proto', req.protocol);
      proxyReq.setHeader('X-Forwarded-Host', req.get('host') || '');
      
      // Log request
      console.log(`[PROXY] ${req.method} ${req.path} -> ${config.target}${proxyReq.path}`);
    },
    onProxyRes: (proxyRes, req: Request, res: Response) => {
      // Add CORS headers
      proxyRes.headers['Access-Control-Allow-Origin'] = '*';
      proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,PUT,POST,DELETE,PATCH,OPTIONS';
      proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Content-Length, X-Requested-With';
      
      // Log response
      console.log(`[PROXY] ${req.method} ${req.path} <- ${proxyRes.statusCode}`);
    }
  };

  return createProxyMiddleware(proxyOptions);
}

/**
 * Main proxy middleware that handles all API routes
 */
export function createDynamicProxyMiddleware() {
  return (req: Request, res: Response, next: NextFunction) => {
    const originalPath = getOriginalPath(req.path);
    
    // Find matching API route configuration
    const routeConfig = appConfig.apiRoutes.find(route => {
      const pattern = route.path.replace(/:([^/]+)/g, '([^/]+)');
      const regex = new RegExp(`^${pattern}$`);
      return regex.test(originalPath) && route.methods.includes(req.method as any);
    });

    if (!routeConfig) {
      return next(); // Let other middleware handle it
    }

    // Apply rate limiting if configured
    if (routeConfig.rateLimit) {
      const rateLimitHandler = rateLimitMiddleware(routeConfig);
      rateLimitHandler(req as ProxyRequest, res, (err?: any) => {
        if (err) return next(err);
        
        // Apply proxy or mock response
        const proxyHandler = createApiProxyMiddleware(routeConfig);
        proxyHandler(req, res, next);
      });
    } else {
      // Apply proxy or mock response directly
      const proxyHandler = createApiProxyMiddleware(routeConfig);
      proxyHandler(req, res, next);
    }
  };
}

/**
 * Health check endpoint
 */
export function healthCheckMiddleware(req: Request, res: Response, next: NextFunction) {
  if (req.path === getMappedPath('/api/health')) {
    return res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    });
  }
  next();
}

/**
 * API documentation endpoint
 */
export function apiDocsMiddleware(req: Request, res: Response, next: NextFunction) {
  if (req.path === getMappedPath('/api/docs')) {
    const routes = appConfig.apiRoutes.map(route => ({
      path: getMappedPath(route.path),
      methods: route.methods,
      proxy: route.proxy,
      target: route.target,
      rateLimit: route.rateLimit
    }));

    return res.json({
      title: 'API Documentation',
      version: '1.0.0',
      baseUrl: req.protocol + '://' + req.get('host'),
      routes
    });
  }
  next();
}

/**
 * Error handling middleware for API routes
 */
export function apiErrorHandler(err: Error, req: Request, res: Response, next: NextFunction) {
  if (!req.path.startsWith(getMappedPath('/api'))) {
    return next(err);
  }

  console.error('API Error:', err);

  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(500).json({
    error: 'Internal Server Error',
    message: isDevelopment ? err.message : 'Something went wrong',
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
    ...(isDevelopment && { stack: err.stack })
  });
}

/**
 * CORS middleware for API routes
 */
export function corsMiddleware(req: Request, res: Response, next: NextFunction) {
  if (!req.path.startsWith(getMappedPath('/api'))) {
    return next();
  }

  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,PATCH,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');

  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }

  next();
}
