import express from 'express';
import compression from 'compression';
import helmet from 'helmet';
import cors from 'cors';
import { createRequestHandler } from '@react-router/express';
import {
  createDynamicProxyMiddleware,
  healthCheckMiddleware,
  apiDocsMiddleware,
  apiErrorHandler,
  corsMiddleware
} from './proxy-middleware';

/**
 * Express server with integrated proxy middleware
 * Handles both React Router SSR and API proxying
 */

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  }
}));

// Compression middleware
app.use(compression());

// CORS middleware for API routes
app.use(corsMiddleware);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static file serving
app.use('/assets', express.static('build/client/assets', {
  immutable: true,
  maxAge: '1y'
}));
app.use(express.static('build/client', { maxAge: '1h' }));

// Health check endpoint
app.use(healthCheckMiddleware);

// API documentation endpoint
app.use(apiDocsMiddleware);

// Dynamic proxy middleware for API routes
app.use(createDynamicProxyMiddleware());

// React Router request handler
const viteDevServer = process.env.NODE_ENV === 'production' 
  ? undefined 
  : await import('vite').then(vite => 
      vite.createServer({
        server: { middlewareMode: true }
      })
    );

if (viteDevServer) {
  app.use(viteDevServer.ssrLoadModule);
} else {
  // Production mode - serve built assets
  app.use('/assets', express.static('build/client/assets'));
}

// Handle React Router routes
app.all('*', createRequestHandler({
  build: viteDevServer
    ? () => viteDevServer.ssrLoadModule('virtual:react-router/server-build')
    : await import('../../build/server/index.js')
}));

// API error handling middleware
app.use(apiErrorHandler);

// Global error handler
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Unhandled error:', err);
  
  if (res.headersSent) {
    return next(err);
  }

  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(500).json({
    error: 'Internal Server Error',
    message: isDevelopment ? err.message : 'Something went wrong',
    timestamp: new Date().toISOString(),
    ...(isDevelopment && { stack: err.stack })
  });
});

const port = process.env.PORT || 3000;

app.listen(port, () => {
  console.log(`🚀 Server running on http://localhost:${port}`);
  console.log(`📚 API docs available at http://localhost:${port}/api/docs`);
  console.log(`❤️  Health check at http://localhost:${port}/api/health`);
});

export default app;
