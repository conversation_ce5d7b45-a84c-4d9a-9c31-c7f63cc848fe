/**
 * useAuth Hook
 *
 * Simple authentication state hook that relies strictly on HTTP-only secure cookies.
 * Never exposes tokens or sensitive authentication data to JavaScript context.
 *
 * Security Features:
 * - HTTP-only cookie authentication
 * - No token storage in JavaScript
 * - Automatic session validation
 * - Secure logout functionality
 * - CSRF protection
 *
 * @example
 * const { isAuthenticated, user, login, logout, isLoading } = useAuth();
 *
 * // Check authentication status
 * if (isAuthenticated) {
 *   return <AuthenticatedApp user={user} />;
 * }
 *
 * // Login with credentials
 * await login({ email, password });
 */

import {
  useState,
  useEffect,
  useCallback,
  createContext,
  useContext,
} from "react";
import type { User, AuthState, LoginCredentials } from "../types";
import { useHttp } from "./useHttp";

// Authentication context for global state management
interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  login: (
    credentials: LoginCredentials
  ) => Promise<{ success: boolean; user?: User }>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  checkAuthStatus: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * Authentication provider component
 */
export function AuthProvider({
  children,
}: AuthProviderProps): React.ReactElement {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { request } = useHttp();

  /**
   * Check current authentication status
   */
  const checkAuthStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await request("/auth/me");

      if (response && response.user) {
        setIsAuthenticated(true);
        setUser(response.user);
      } else {
        setIsAuthenticated(false);
        setUser(null);
      }
    } catch (error) {
      // If auth check fails, user is not authenticated
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }, [request]);

  /**
   * Login with credentials
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   * @returns {Promise<Object>} Login result
   */
  const login = useCallback(
    async (credentials) => {
      try {
        setIsLoading(true);
        const response = await request("/auth/login", {
          method: "POST",
          body: credentials,
        });

        if (response && response.user) {
          setIsAuthenticated(true);
          setUser(response.user);
          return { success: true, user: response.user };
        } else {
          throw new Error("Invalid login response");
        }
      } catch (error) {
        setIsAuthenticated(false);
        setUser(null);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [request]
  );

  /**
   * Logout current user
   * @returns {Promise<void>}
   */
  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await request("/auth/logout", { method: "POST" });
    } catch (error) {
      // Even if logout request fails, clear local state
      console.warn("Logout request failed:", error);
    } finally {
      setIsAuthenticated(false);
      setUser(null);
      setIsLoading(false);
    }
  }, [request]);

  /**
   * Refresh user data
   */
  const refreshUser = useCallback(async () => {
    if (isAuthenticated) {
      await checkAuthStatus();
    }
  }, [isAuthenticated, checkAuthStatus]);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  // Listen for storage events (logout from other tabs)
  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === "auth_logout") {
        setIsAuthenticated(false);
        setUser(null);
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  const value = {
    isAuthenticated,
    user,
    isLoading,
    login,
    logout,
    refreshUser,
    checkAuthStatus,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Custom hook to use authentication context
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
}
