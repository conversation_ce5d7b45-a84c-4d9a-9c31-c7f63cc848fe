/**
 * useResize Hook
 *
 * A responsive design hook that provides window size information and breakpoint utilities.
 * Triggers on window resize events with debouncing for performance optimization.
 *
 * Features:
 * - Real-time window dimensions
 * - Responsive breakpoint detection
 * - Debounced resize events
 * - Orientation change detection
 * - Performance optimized
 *
 * @example
 * const { windowSize, isMobile, isTablet, isDesktop, orientation } = useResize();
 *
 * // Conditional rendering based on screen size
 * if (isMobile) {
 *   return <MobileComponent />;
 * }
 *
 * // Use window dimensions
 * const containerWidth = windowSize.width * 0.8;
 */

import { useState, useEffect, useCallback } from "react";
import type { WindowSize, ResponsiveBreakpoints } from "../types";

// Responsive breakpoints (in pixels)
const BREAKPOINTS: ResponsiveBreakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200,
};

// Types for the hook
interface UseResizeReturn {
  windowSize: WindowSize;
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeScreen: boolean;
  orientation: "portrait" | "landscape";
  aspectRatio: number;
  getResponsiveValue: <T>(
    mobileValue: T,
    tabletValue?: T,
    desktopValue?: T
  ) => T;
  isBreakpoint: (
    breakpoint: "mobile" | "tablet" | "desktop" | "large"
  ) => boolean;
  breakpoints: ResponsiveBreakpoints;
}

/**
 * Debounce utility function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Get current window dimensions
 * @returns {Object} Window width and height
 */
function getWindowSize(): WindowSize {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
  };
}

/**
 * Get device orientation
 * @param {number} width - Window width
 * @param {number} height - Window height
 * @returns {string} Orientation ('portrait' or 'landscape')
 */
function getOrientation(
  width: number,
  height: number
): "portrait" | "landscape" {
  return width > height ? "landscape" : "portrait";
}

/**
 * Custom hook for responsive design utilities
 * @param {number} debounceMs - Debounce delay in milliseconds (default: 150)
 * @returns {Object} Resize utilities and breakpoint information
 */
export function useResize(debounceMs: number = 150): UseResizeReturn {
  const [windowSize, setWindowSize] = useState<WindowSize>(() => {
    // Handle SSR by providing default values
    if (typeof window === "undefined") {
      return { width: 1200, height: 800 };
    }
    return getWindowSize();
  });

  // Memoized resize handler with debouncing
  const handleResize = useCallback(
    debounce(() => {
      setWindowSize(getWindowSize());
    }, debounceMs),
    [debounceMs]
  );

  useEffect(() => {
    // Set initial size on mount (handles SSR)
    if (typeof window !== "undefined") {
      setWindowSize(getWindowSize());
    }

    // Add event listeners
    window.addEventListener("resize", handleResize);
    window.addEventListener("orientationchange", handleResize);

    // Cleanup
    return () => {
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("orientationchange", handleResize);
    };
  }, [handleResize]);

  // Calculate responsive breakpoints
  const isMobile = windowSize.width < BREAKPOINTS.mobile;
  const isTablet =
    windowSize.width >= BREAKPOINTS.mobile &&
    windowSize.width < BREAKPOINTS.tablet;
  const isDesktop = windowSize.width >= BREAKPOINTS.desktop;
  const isLargeScreen = windowSize.width >= BREAKPOINTS.tablet;

  // Device orientation
  const orientation = getOrientation(windowSize.width, windowSize.height);

  // Aspect ratio
  const aspectRatio = windowSize.width / windowSize.height;

  // Utility functions for common responsive patterns
  const getResponsiveValue = useCallback(
    (mobileValue, tabletValue, desktopValue) => {
      if (isMobile) return mobileValue;
      if (isTablet) return tabletValue || mobileValue;
      return desktopValue || tabletValue || mobileValue;
    },
    [isMobile, isTablet]
  );

  const isBreakpoint = useCallback(
    (breakpoint) => {
      switch (breakpoint) {
        case "mobile":
          return isMobile;
        case "tablet":
          return isTablet;
        case "desktop":
          return isDesktop;
        case "large":
          return isLargeScreen;
        default:
          return false;
      }
    },
    [isMobile, isTablet, isDesktop, isLargeScreen]
  );

  return {
    // Window dimensions
    windowSize,
    width: windowSize.width,
    height: windowSize.height,

    // Breakpoint flags
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,

    // Device information
    orientation,
    aspectRatio,

    // Utility functions
    getResponsiveValue,
    isBreakpoint,

    // Breakpoint constants (for external use)
    breakpoints: BREAKPOINTS,
  };
}
