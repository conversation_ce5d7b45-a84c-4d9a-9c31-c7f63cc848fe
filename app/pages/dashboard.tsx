/**
 * Dashboard Page
 * 
 * Demonstrates the integration of all major components:
 * - Ant Design components
 * - AG Grid data tables
 * - Font Awesome icons
 * - Custom hooks
 * - Responsive design
 * - TypeScript integration
 */

import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Button, Space, Typography, Statistic, Table, Tag } from 'antd';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers, faChartLine, faCog, faShieldAlt } from '@fortawesome/free-solid-svg-icons';
import { AgGridReact } from 'ag-grid-react';
import { useAuth } from '../hooks/useAuth';
import { useHttp } from '../hooks/useHttp';
import { useResize } from '../hooks/useResize';
import type { User } from '../types';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';

const { Title, Text } = Typography;

// Sample data for demonstration
const sampleUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Admin',
    roles: ['admin'],
    isAuthenticated: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'User',
    roles: ['user'],
    isAuthenticated: true,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
  },
];

// AG Grid column definitions
const columnDefs = [
  { 
    field: 'firstName', 
    headerName: 'First Name', 
    sortable: true, 
    filter: true,
    width: 150
  },
  { 
    field: 'lastName', 
    headerName: 'Last Name', 
    sortable: true, 
    filter: true,
    width: 150
  },
  { 
    field: 'email', 
    headerName: 'Email', 
    sortable: true, 
    filter: true,
    width: 250
  },
  { 
    field: 'roles', 
    headerName: 'Roles', 
    width: 150,
    cellRenderer: (params: any) => {
      return params.value.map((role: string) => (
        <Tag key={role} color={role === 'admin' ? 'red' : 'blue'}>
          {role.toUpperCase()}
        </Tag>
      ));
    }
  },
  { 
    field: 'createdAt', 
    headerName: 'Created', 
    sortable: true,
    width: 150,
    cellRenderer: (params: any) => {
      return new Date(params.value).toLocaleDateString();
    }
  },
];

/**
 * Dashboard component showcasing all integrated features
 */
export default function Dashboard(): React.ReactElement {
  const { user, isAuthenticated } = useAuth();
  const { request, loading, error } = useHttp();
  const { isMobile, isTablet, windowSize } = useResize();
  const [users, setUsers] = useState<User[]>(sampleUsers);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    adminUsers: 0,
    growth: 0,
  });

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        // In a real app, these would be API calls
        // const usersData = await request<User[]>('/api/users');
        // const statsData = await request('/api/dashboard/stats');
        
        // For demo purposes, use sample data
        setUsers(sampleUsers);
        setStats({
          totalUsers: sampleUsers.length,
          activeUsers: sampleUsers.filter(u => u.isAuthenticated).length,
          adminUsers: sampleUsers.filter(u => u.roles.includes('admin')).length,
          growth: 12.5,
        });
      } catch (err) {
        console.error('Failed to load dashboard data:', err);
      }
    };

    if (isAuthenticated) {
      loadDashboardData();
    }
  }, [isAuthenticated, request]);

  // Handle user actions
  const handleRefresh = async () => {
    // Simulate data refresh
    console.log('Refreshing dashboard data...');
  };

  const handleExport = () => {
    // Simulate data export
    console.log('Exporting data...');
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card>
          <Title level={3}>Please log in to access the dashboard</Title>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <Title level={2}>
          <FontAwesomeIcon icon={faChartLine} className="mr-3 text-blue-600" />
          Dashboard
        </Title>
        <Text type="secondary">
          Welcome back, {user?.firstName}! Here's your overview.
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={stats.totalUsers}
              prefix={<FontAwesomeIcon icon={faUsers} />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={stats.activeUsers}
              prefix={<FontAwesomeIcon icon={faShieldAlt} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Admin Users"
              value={stats.adminUsers}
              prefix={<FontAwesomeIcon icon={faCog} />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Growth"
              value={stats.growth}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Actions */}
      <div className="mb-4">
        <Space>
          <Button type="primary" onClick={handleRefresh} loading={loading}>
            Refresh Data
          </Button>
          <Button onClick={handleExport}>
            Export Data
          </Button>
        </Space>
      </div>

      {/* Data Table */}
      <Card title="Users Management" className="mb-6">
        <div className={`ag-theme-alpine ${isMobile ? 'h-64' : 'h-96'}`}>
          <AgGridReact
            columnDefs={columnDefs}
            rowData={users}
            pagination={true}
            paginationPageSize={isMobile ? 5 : 10}
            sortingOrder={['asc', 'desc']}
            enableRangeSelection={!isMobile}
            animateRows={true}
            defaultColDef={{
              resizable: true,
              sortable: true,
              filter: true,
            }}
          />
        </div>
      </Card>

      {/* Responsive Information */}
      <Card title="Responsive Design Demo">
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Text strong>Current Screen Size:</Text>
            <br />
            <Text>Width: {windowSize.width}px</Text>
            <br />
            <Text>Height: {windowSize.height}px</Text>
          </Col>
          <Col xs={24} md={12}>
            <Text strong>Device Type:</Text>
            <br />
            <Tag color={isMobile ? 'green' : 'default'}>Mobile</Tag>
            <Tag color={isTablet ? 'green' : 'default'}>Tablet</Tag>
            <Tag color={!isMobile && !isTablet ? 'green' : 'default'}>Desktop</Tag>
          </Col>
        </Row>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="mt-4" style={{ borderColor: '#ff4d4f' }}>
          <Text type="danger">Error: {error}</Text>
        </Card>
      )}
    </div>
  );
}
