import { Outlet, Link, useLocation, useNavigate } from "react-router";
import { useState, useEffect } from "react";
import { Layout, Menu, Breadcrumb, Avatar, Dropdown, Button, theme } from "antd";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
  LogoutOutlined,
  BellOutlined,
} from "@ant-design/icons";
import { getMappedPath } from "../../config/routes.config";
import { generateBreadcrumbs, getAdminNavigation } from "../../utils/route-generator";
import type { Route } from "./+types/layout";

const { Header, Sider, Content } = Layout;
const { useToken } = theme;

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Admin Panel" },
    { name: "description", content: "Secure admin panel for application management" },
  ];
}

export default function AdminLayout() {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { token } = useToken();

  // Get navigation items
  const navigationItems = getAdminNavigation();
  
  // Generate breadcrumbs
  const breadcrumbs = generateBreadcrumbs(location.pathname);

  // Convert navigation to Ant Design menu items
  const menuItems = [
    {
      key: getMappedPath("/admin"),
      icon: <DashboardOutlined />,
      label: "Dashboard",
      onClick: () => navigate(getMappedPath("/admin"))
    },
    ...navigationItems.map(item => ({
      key: item.key,
      icon: getMenuIcon(item.key),
      label: item.label,
      children: item.children?.map(child => ({
        key: child.key,
        label: child.label,
        onClick: () => navigate(child.path)
      })),
      onClick: item.children ? undefined : () => navigate(item.path)
    }))
  ];

  // Get current selected menu key
  const selectedKey = location.pathname;
  const openKeys = getOpenKeys(location.pathname);

  // User dropdown menu
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: handleLogout,
    },
  ];

  function getMenuIcon(key: string) {
    switch (key) {
      case 'users':
        return <UserOutlined />;
      case 'settings':
        return <SettingOutlined />;
      case 'reports':
        return <BarChartOutlined />;
      default:
        return <DashboardOutlined />;
    }
  }

  function getOpenKeys(pathname: string): string[] {
    const adminBasePath = getMappedPath("/admin");
    const relativePath = pathname.replace(adminBasePath, "").replace(/^\//, "");
    const segments = relativePath.split("/").filter(Boolean);
    
    if (segments.length > 0) {
      return [segments[0]];
    }
    
    return [];
  }

  function handleLogout() {
    // Clear authentication and redirect
    localStorage.removeItem('auth_token');
    navigate('/');
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: token.colorBgContainer,
          borderRight: `1px solid ${token.colorBorder}`,
        }}
      >
        <div style={{
          height: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: `1px solid ${token.colorBorder}`,
          fontSize: collapsed ? 16 : 20,
          fontWeight: 'bold',
          color: token.colorPrimary,
        }}>
          {collapsed ? 'AP' : 'Admin Panel'}
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          defaultOpenKeys={openKeys}
          items={menuItems}
          style={{ borderRight: 0 }}
        />
      </Sider>
      
      <Layout>
        <Header style={{
          padding: '0 16px',
          background: token.colorBgContainer,
          borderBottom: `1px solid ${token.colorBorder}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: 16, width: 64, height: 64 }}
            />
            
            <Breadcrumb
              style={{ marginLeft: 16 }}
              items={breadcrumbs.map(crumb => ({
                title: crumb.path === location.pathname ? (
                  <span>{crumb.label}</span>
                ) : (
                  <Link to={crumb.path}>{crumb.label}</Link>
                )
              }))}
            />
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Button
              type="text"
              icon={<BellOutlined />}
              style={{ fontSize: 16 }}
            />
            
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                padding: '8px 12px',
                borderRadius: token.borderRadius,
                transition: 'background-color 0.2s',
              }}>
                <Avatar size="small" icon={<UserOutlined />} />
                <span style={{ marginLeft: 8, fontSize: 14 }}>Admin User</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        
        <Content style={{
          margin: 24,
          padding: 24,
          background: token.colorBgContainer,
          borderRadius: token.borderRadius,
          minHeight: 280,
        }}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
}
