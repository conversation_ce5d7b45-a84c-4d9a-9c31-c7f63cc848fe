import { useEffect, useState } from "react";
import { Card, Row, Col, Statistic, Table, Progress, Tag, Button } from "antd";
import {
  UserOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { apiClient } from "../../utils/api-client";
import type { Route } from "./+types/dashboard";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Dashboard - Admin Panel" },
    {
      name: "description",
      content: "Admin dashboard with key metrics and analytics",
    },
  ];
}

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  revenue: number;
  growth: number;
}

interface RecentActivity {
  id: string;
  user: string;
  action: string;
  timestamp: string;
  status: "success" | "warning" | "error";
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load dashboard stats
      const statsResponse = await apiClient.get<DashboardStats>(
        "/api/dashboard/stats"
      );
      setStats(statsResponse.data);

      // Mock recent activity data
      setRecentActivity([
        {
          id: "1",
          user: "John Doe",
          action: "Created new user account",
          timestamp: "2 minutes ago",
          status: "success",
        },
        {
          id: "2",
          user: "Jane Smith",
          action: "Updated profile settings",
          timestamp: "5 minutes ago",
          status: "success",
        },
        {
          id: "3",
          user: "Bob Johnson",
          action: "Failed login attempt",
          timestamp: "10 minutes ago",
          status: "error",
        },
        {
          id: "4",
          user: "Alice Brown",
          action: "Uploaded new document",
          timestamp: "15 minutes ago",
          status: "success",
        },
        {
          id: "5",
          user: "Charlie Wilson",
          action: "Password reset requested",
          timestamp: "20 minutes ago",
          status: "warning",
        },
      ]);
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const activityColumns = [
    {
      title: "User",
      dataIndex: "user",
      key: "user",
    },
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => {
        const color =
          status === "success"
            ? "green"
            : status === "warning"
            ? "orange"
            : "red";
        return <Tag color={color}>{status.toUpperCase()}</Tag>;
      },
    },
    {
      title: "Time",
      dataIndex: "timestamp",
      key: "timestamp",
    },
  ];

  return (
    <div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: 24,
        }}
      >
        <h1 style={{ margin: 0, fontSize: 24, fontWeight: 600 }}>Dashboard</h1>
        <Button
          icon={<ReloadOutlined />}
          onClick={loadDashboardData}
          loading={loading}
        >
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={stats?.totalUsers || 0}
              prefix={<UserOutlined />}
              loading={loading}
              valueStyle={{ color: "#3f8600" }}
              suffix={
                <span style={{ fontSize: 14, color: "#52c41a" }}>
                  <ArrowUpOutlined /> 12%
                </span>
              }
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={stats?.activeUsers || 0}
              prefix={<TrophyOutlined />}
              loading={loading}
              valueStyle={{ color: "#1890ff" }}
              suffix={
                <span style={{ fontSize: 14, color: "#1890ff" }}>
                  <ArrowUpOutlined /> 8%
                </span>
              }
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Revenue"
              value={stats?.revenue || 0}
              prefix={<DollarOutlined />}
              precision={2}
              loading={loading}
              valueStyle={{ color: "#722ed1" }}
              suffix={
                <span style={{ fontSize: 14, color: "#722ed1" }}>
                  <ArrowUpOutlined /> 15%
                </span>
              }
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Growth Rate"
              value={stats?.growth || 0}
              suffix="%"
              loading={loading}
              valueStyle={{
                color: stats && stats.growth > 0 ? "#3f8600" : "#cf1322",
              }}
              prefix={
                stats && stats.growth > 0 ? (
                  <ArrowUpOutlined />
                ) : (
                  <ArrowDownOutlined />
                )
              }
            />
          </Card>
        </Col>
      </Row>

      {/* Charts and Activity */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="Performance Overview" loading={loading}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: 8,
                    }}
                  >
                    <span>Server Performance</span>
                    <span>85%</span>
                  </div>
                  <Progress percent={85} status="active" />
                </div>

                <div style={{ marginBottom: 16 }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: 8,
                    }}
                  >
                    <span>Database Performance</span>
                    <span>92%</span>
                  </div>
                  <Progress
                    percent={92}
                    status="active"
                    strokeColor="#52c41a"
                  />
                </div>

                <div>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: 8,
                    }}
                  >
                    <span>API Response Time</span>
                    <span>78%</span>
                  </div>
                  <Progress
                    percent={78}
                    status="active"
                    strokeColor="#1890ff"
                  />
                </div>
              </Col>

              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: 8,
                    }}
                  >
                    <span>Memory Usage</span>
                    <span>65%</span>
                  </div>
                  <Progress
                    percent={65}
                    status="active"
                    strokeColor="#722ed1"
                  />
                </div>

                <div style={{ marginBottom: 16 }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: 8,
                    }}
                  >
                    <span>CPU Usage</span>
                    <span>45%</span>
                  </div>
                  <Progress
                    percent={45}
                    status="active"
                    strokeColor="#fa8c16"
                  />
                </div>

                <div>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      marginBottom: 8,
                    }}
                  >
                    <span>Disk Usage</span>
                    <span>58%</span>
                  </div>
                  <Progress
                    percent={58}
                    status="active"
                    strokeColor="#13c2c2"
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="Recent Activity" loading={loading}>
            <Table
              dataSource={recentActivity}
              columns={activityColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
}
