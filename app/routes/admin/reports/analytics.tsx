import { Card } from "antd";
import type { Route } from "./+types/analytics";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Analytics - Admin Panel" },
    { name: "description", content: "Detailed analytics and metrics" },
  ];
}

export default function Analytics() {
  return (
    <div>
      <h1 style={{ marginBottom: 24, fontSize: 24, fontWeight: 600 }}>Analytics</h1>
      <Card>
        <p>Analytics will be implemented here.</p>
      </Card>
    </div>
  );
}
