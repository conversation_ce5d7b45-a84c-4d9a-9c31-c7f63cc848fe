import { Card } from "antd";
import type { Route } from "./+types/integrations";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Integrations - Admin Panel" },
    { name: "description", content: "Third-party integrations and API settings" },
  ];
}

export default function Integrations() {
  return (
    <div>
      <h1 style={{ marginBottom: 24, fontSize: 24, fontWeight: 600 }}>Integrations</h1>
      <Card>
        <p>Integration settings will be implemented here.</p>
      </Card>
    </div>
  );
}
