import { Card } from "antd";
import type { Route } from "./+types/security";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Security Settings - Admin Panel" },
    { name: "description", content: "Security and authentication settings" },
  ];
}

export default function SecuritySettings() {
  return (
    <div>
      <h1 style={{ marginBottom: 24, fontSize: 24, fontWeight: 600 }}>Security Settings</h1>
      <Card>
        <p>Security settings will be implemented here.</p>
      </Card>
    </div>
  );
}
