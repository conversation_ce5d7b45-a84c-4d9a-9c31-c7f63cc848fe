import { Card, Form, Input, Switch, Button, Space, message } from "antd";
import { useState } from "react";
import type { Route } from "./+types/general";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "General Settings - Admin Panel" },
    { name: "description", content: "General application settings" },
  ];
}

export default function GeneralSettings() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('Settings saved successfully');
    } catch (error) {
      message.error('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h1 style={{ marginBottom: 24, fontSize: 24, fontWeight: 600 }}>General Settings</h1>
      
      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            appName: 'Admin Panel',
            appDescription: 'Secure admin panel for application management',
            maintenanceMode: false,
            registrationEnabled: true,
            emailNotifications: true
          }}
        >
          <Form.Item
            label="Application Name"
            name="appName"
            rules={[{ required: true, message: 'Please enter application name' }]}
          >
            <Input placeholder="Enter application name" />
          </Form.Item>

          <Form.Item
            label="Application Description"
            name="appDescription"
          >
            <Input.TextArea rows={3} placeholder="Enter application description" />
          </Form.Item>

          <Form.Item
            label="Maintenance Mode"
            name="maintenanceMode"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="User Registration"
            name="registrationEnabled"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="Email Notifications"
            name="emailNotifications"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Save Settings
              </Button>
              <Button onClick={() => form.resetFields()}>
                Reset
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}
