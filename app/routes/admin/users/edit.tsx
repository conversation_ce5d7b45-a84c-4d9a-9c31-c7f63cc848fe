import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router";
import { Card, Form, Input, Select, Button, Space, message, Spin } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { apiClient } from "../../../utils/api-client";
import { getMappedPath } from "../../../config/routes.config";
import type { Route } from "./+types/edit";

const { Option } = Select;
const { TextArea } = Input;

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Edit User - Admin Panel" },
    { name: "description", content: "Edit user information and settings" },
  ];
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
  phone?: string;
  address?: string;
  bio?: string;
}

export default function EditUser() {
  const { id } = useParams();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    if (id) {
      loadUser(id);
    }
  }, [id]);

  const loadUser = async (userId: string) => {
    try {
      setLoading(true);
      
      // Mock data for demonstration
      const mockUser: User = {
        id: userId,
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        phone: '+****************',
        address: '123 Main St, New York, NY 10001',
        bio: 'Experienced administrator with 5+ years in system management.'
      };

      setUser(mockUser);
      form.setFieldsValue(mockUser);
    } catch (error) {
      console.error('Failed to load user:', error);
      message.error('Failed to load user');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: Partial<User>) => {
    try {
      setSaving(true);
      
      // Update user via API
      await apiClient.put(`/api/users/${id}`, values);
      
      message.success('User updated successfully');
      navigate(getMappedPath(`/admin/users/${id}`));
    } catch (error) {
      console.error('Failed to update user:', error);
      message.error('Failed to update user');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!user) {
    return (
      <div style={{ textAlign: 'center', padding: 48 }}>
        <h2>User not found</h2>
        <Button type="primary" onClick={() => navigate(getMappedPath('/admin/users'))}>
          Back to Users
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate(getMappedPath(`/admin/users/${id}`))}
          style={{ marginBottom: 16 }}
        >
          Back to User Details
        </Button>
        
        <h1 style={{ margin: 0, fontSize: 24, fontWeight: 600 }}>Edit User</h1>
      </div>

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="Full Name"
            name="name"
            rules={[
              { required: true, message: 'Please enter the user\'s full name' },
              { min: 2, message: 'Name must be at least 2 characters' }
            ]}
          >
            <Input placeholder="Enter full name" />
          </Form.Item>

          <Form.Item
            label="Email"
            name="email"
            rules={[
              { required: true, message: 'Please enter the user\'s email' },
              { type: 'email', message: 'Please enter a valid email address' }
            ]}
          >
            <Input placeholder="Enter email address" />
          </Form.Item>

          <Form.Item
            label="Role"
            name="role"
            rules={[{ required: true, message: 'Please select a role' }]}
          >
            <Select placeholder="Select role">
              <Option value="user">User</Option>
              <Option value="moderator">Moderator</Option>
              <Option value="admin">Admin</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Status"
            name="status"
            rules={[{ required: true, message: 'Please select a status' }]}
          >
            <Select placeholder="Select status">
              <Option value="active">Active</Option>
              <Option value="inactive">Inactive</Option>
              <Option value="pending">Pending</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Phone"
            name="phone"
            rules={[
              { pattern: /^\+?[\d\s\-\(\)]+$/, message: 'Please enter a valid phone number' }
            ]}
          >
            <Input placeholder="Enter phone number (optional)" />
          </Form.Item>

          <Form.Item
            label="Address"
            name="address"
          >
            <TextArea 
              rows={3} 
              placeholder="Enter address (optional)" 
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item
            label="Bio"
            name="bio"
          >
            <TextArea 
              rows={4} 
              placeholder="Enter bio (optional)" 
              maxLength={1000}
              showCount
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={saving}>
                Update User
              </Button>
              <Button onClick={() => form.resetFields()}>
                Reset
              </Button>
              <Button onClick={() => navigate(getMappedPath(`/admin/users/${id}`))}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}
