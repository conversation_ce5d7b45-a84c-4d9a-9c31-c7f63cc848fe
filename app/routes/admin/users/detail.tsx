import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, <PERSON> } from "react-router";
import { Card, Descriptions, Tag, Button, Space, Avatar, Divider, Spin } from "antd";
import { EditOutlined, ArrowLeftOutlined, UserOutlined } from "@ant-design/icons";
import { apiClient } from "../../../utils/api-client";
import { getMappedPath } from "../../../config/routes.config";
import type { Route } from "./+types/detail";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "User Details - Admin Panel" },
    { name: "description", content: "View user details and information" },
  ];
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'pending';
  lastLogin: string;
  createdAt: string;
  phone?: string;
  address?: string;
  bio?: string;
  avatar?: string;
}

export default function UserDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadUser(id);
    }
  }, [id]);

  const loadUser = async (userId: string) => {
    try {
      setLoading(true);
      
      // Mock data for demonstration
      const mockUser: User = {
        id: userId,
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        lastLogin: '2024-01-15 10:30:00',
        createdAt: '2024-01-01 09:00:00',
        phone: '+****************',
        address: '123 Main St, New York, NY 10001',
        bio: 'Experienced administrator with 5+ years in system management.',
        avatar: undefined
      };

      setUser(mockUser);
    } catch (error) {
      console.error('Failed to load user:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'red';
      case 'pending':
        return 'orange';
      default:
        return 'default';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'purple';
      case 'moderator':
        return 'blue';
      case 'user':
        return 'default';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!user) {
    return (
      <div style={{ textAlign: 'center', padding: 48 }}>
        <h2>User not found</h2>
        <Button type="primary" onClick={() => navigate(getMappedPath('/admin/users'))}>
          Back to Users
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate(getMappedPath('/admin/users'))}
          style={{ marginBottom: 16 }}
        >
          Back to Users
        </Button>
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={{ margin: 0, fontSize: 24, fontWeight: 600 }}>User Details</h1>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => navigate(getMappedPath(`/admin/users/${user.id}/edit`))}
          >
            Edit User
          </Button>
        </div>
      </div>

      <Card>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
          <Avatar
            size={80}
            src={user.avatar}
            icon={<UserOutlined />}
            style={{ marginRight: 16 }}
          />
          <div>
            <h2 style={{ margin: 0, fontSize: 20, fontWeight: 600 }}>{user.name}</h2>
            <p style={{ margin: 0, color: '#666', fontSize: 16 }}>{user.email}</p>
            <Space style={{ marginTop: 8 }}>
              <Tag color={getRoleColor(user.role)}>{user.role.toUpperCase()}</Tag>
              <Tag color={getStatusColor(user.status)}>{user.status.toUpperCase()}</Tag>
            </Space>
          </div>
        </div>

        <Divider />

        <Descriptions title="Personal Information" bordered column={2}>
          <Descriptions.Item label="Full Name">{user.name}</Descriptions.Item>
          <Descriptions.Item label="Email">{user.email}</Descriptions.Item>
          <Descriptions.Item label="Phone">{user.phone || 'Not provided'}</Descriptions.Item>
          <Descriptions.Item label="Role">
            <Tag color={getRoleColor(user.role)}>{user.role.toUpperCase()}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Status">
            <Tag color={getStatusColor(user.status)}>{user.status.toUpperCase()}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Last Login">
            {user.lastLogin === 'Never' ? 'Never' : new Date(user.lastLogin).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="Created At">
            {new Date(user.createdAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="Address" span={2}>
            {user.address || 'Not provided'}
          </Descriptions.Item>
          <Descriptions.Item label="Bio" span={2}>
            {user.bio || 'No bio provided'}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  );
}
