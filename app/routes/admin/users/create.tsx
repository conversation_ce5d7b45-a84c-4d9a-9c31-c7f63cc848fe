import { useState } from "react";
import { useNavigate } from "react-router";
import { Card, Form, Input, Select, Button, Space, message, Upload } from "antd";
import { ArrowLeftOutlined, UploadOutlined } from "@ant-design/icons";
import { apiClient } from "../../../utils/api-client";
import { getMappedPath } from "../../../config/routes.config";
import type { Route } from "./+types/create";

const { Option } = Select;
const { TextArea } = Input;

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Create User - Admin Panel" },
    { name: "description", content: "Create a new user account" },
  ];
}

interface CreateUserForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: string;
  status: string;
  phone?: string;
  address?: string;
  bio?: string;
}

export default function CreateUser() {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: CreateUserForm) => {
    try {
      setLoading(true);
      
      // Validate password confirmation
      if (values.password !== values.confirmPassword) {
        message.error('Passwords do not match');
        return;
      }

      // Remove confirmPassword from the payload
      const { confirmPassword, ...userData } = values;

      // Create user via API
      await apiClient.post('/api/users', userData);
      
      message.success('User created successfully');
      navigate(getMappedPath('/admin/users'));
    } catch (error) {
      console.error('Failed to create user:', error);
      message.error('Failed to create user');
    } finally {
      setLoading(false);
    }
  };

  const validatePassword = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('Password is required'));
    }
    if (value.length < 8) {
      return Promise.reject(new Error('Password must be at least 8 characters'));
    }
    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
      return Promise.reject(new Error('Password must contain at least one uppercase letter, one lowercase letter, and one number'));
    }
    return Promise.resolve();
  };

  const validateConfirmPassword = (_: any, value: string) => {
    const password = form.getFieldValue('password');
    if (value && value !== password) {
      return Promise.reject(new Error('Passwords do not match'));
    }
    return Promise.resolve();
  };

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate(getMappedPath('/admin/users'))}
          style={{ marginBottom: 16 }}
        >
          Back to Users
        </Button>
        
        <h1 style={{ margin: 0, fontSize: 24, fontWeight: 600 }}>Create New User</h1>
      </div>

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
          style={{ maxWidth: 600 }}
        >
          <Form.Item
            label="Full Name"
            name="name"
            rules={[
              { required: true, message: 'Please enter the user\'s full name' },
              { min: 2, message: 'Name must be at least 2 characters' }
            ]}
          >
            <Input placeholder="Enter full name" />
          </Form.Item>

          <Form.Item
            label="Email"
            name="email"
            rules={[
              { required: true, message: 'Please enter the user\'s email' },
              { type: 'email', message: 'Please enter a valid email address' }
            ]}
          >
            <Input placeholder="Enter email address" />
          </Form.Item>

          <Form.Item
            label="Password"
            name="password"
            rules={[{ validator: validatePassword }]}
          >
            <Input.Password placeholder="Enter password" />
          </Form.Item>

          <Form.Item
            label="Confirm Password"
            name="confirmPassword"
            dependencies={['password']}
            rules={[
              { required: true, message: 'Please confirm the password' },
              { validator: validateConfirmPassword }
            ]}
          >
            <Input.Password placeholder="Confirm password" />
          </Form.Item>

          <Form.Item
            label="Role"
            name="role"
            rules={[{ required: true, message: 'Please select a role' }]}
          >
            <Select placeholder="Select role">
              <Option value="user">User</Option>
              <Option value="moderator">Moderator</Option>
              <Option value="admin">Admin</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Status"
            name="status"
            rules={[{ required: true, message: 'Please select a status' }]}
            initialValue="active"
          >
            <Select placeholder="Select status">
              <Option value="active">Active</Option>
              <Option value="inactive">Inactive</Option>
              <Option value="pending">Pending</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Phone"
            name="phone"
            rules={[
              { pattern: /^\+?[\d\s\-\(\)]+$/, message: 'Please enter a valid phone number' }
            ]}
          >
            <Input placeholder="Enter phone number (optional)" />
          </Form.Item>

          <Form.Item
            label="Address"
            name="address"
          >
            <TextArea 
              rows={3} 
              placeholder="Enter address (optional)" 
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item
            label="Bio"
            name="bio"
          >
            <TextArea 
              rows={4} 
              placeholder="Enter bio (optional)" 
              maxLength={1000}
              showCount
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Create User
              </Button>
              <Button onClick={() => form.resetFields()}>
                Reset
              </Button>
              <Button onClick={() => navigate(getMappedPath('/admin/users'))}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}
