import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios';
import { getMappedPath } from '../config/routes.config';

/**
 * Centralized API client with proxy integration
 * Supports all HTTP methods with proper error handling and request/response interceptors
 */

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export interface RequestOptions extends AxiosRequestConfig {
  skipAuth?: boolean;
  retries?: number;
  timeout?: number;
}

class ApiClient {
  private client: AxiosInstance;
  private baseURL: string;
  private authToken: string | null = null;

  constructor(baseURL?: string) {
    this.baseURL = baseURL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add authentication token if available
        if (this.authToken && !config.skipAuth) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }

        // Add request timestamp
        config.metadata = { startTime: Date.now() };

        // Log request in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`);
        }

        return config;
      },
      (error) => {
        console.error('[API] Request error:', error);
        return Promise.reject(this.formatError(error));
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        // Calculate request duration
        const duration = Date.now() - (response.config.metadata?.startTime || 0);
        
        // Log response in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`[API] ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status} (${duration}ms)`);
        }

        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 Unauthorized
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          // Clear auth token
          this.clearAuth();
          
          // Redirect to login if in browser
          if (typeof window !== 'undefined') {
            window.location.href = getMappedPath('/admin/login');
          }
          
          return Promise.reject(this.formatError(error));
        }

        // Handle rate limiting
        if (error.response?.status === 429) {
          const retryAfter = error.response.headers['retry-after'] || 1;
          console.warn(`[API] Rate limited. Retrying after ${retryAfter}s`);
          
          // Auto-retry after delay if retries are enabled
          if (originalRequest.retries && originalRequest.retries > 0) {
            originalRequest.retries--;
            await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
            return this.client(originalRequest);
          }
        }

        // Handle network errors with retry
        if (!error.response && originalRequest.retries && originalRequest.retries > 0) {
          originalRequest.retries--;
          console.warn(`[API] Network error. Retrying... (${originalRequest.retries} attempts left)`);
          await new Promise(resolve => setTimeout(resolve, 1000));
          return this.client(originalRequest);
        }

        console.error('[API] Response error:', error);
        return Promise.reject(this.formatError(error));
      }
    );
  }

  /**
   * Format error for consistent error handling
   */
  private formatError(error: any): ApiError {
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data?.message || error.response.statusText || 'Request failed',
        status: error.response.status,
        code: error.response.data?.code,
        details: error.response.data
      };
    } else if (error.request) {
      // Network error
      return {
        message: 'Network error - please check your connection',
        code: 'NETWORK_ERROR'
      };
    } else {
      // Other error
      return {
        message: error.message || 'An unexpected error occurred',
        code: 'UNKNOWN_ERROR'
      };
    }
  }

  /**
   * Set authentication token
   */
  setAuth(token: string) {
    this.authToken = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  /**
   * Clear authentication token
   */
  clearAuth() {
    this.authToken = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  /**
   * Load authentication token from storage
   */
  loadAuth() {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token');
      if (token) {
        this.authToken = token;
      }
    }
  }

  /**
   * GET request
   */
  async get<T = any>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    const mappedUrl = getMappedPath(url);
    const response = await this.client.get<T>(mappedUrl, { retries: 3, ...options });
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as Record<string, string>
    };
  }

  /**
   * POST request
   */
  async post<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    const mappedUrl = getMappedPath(url);
    const response = await this.client.post<T>(mappedUrl, data, { retries: 2, ...options });
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as Record<string, string>
    };
  }

  /**
   * PUT request
   */
  async put<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    const mappedUrl = getMappedPath(url);
    const response = await this.client.put<T>(mappedUrl, data, { retries: 2, ...options });
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as Record<string, string>
    };
  }

  /**
   * PATCH request
   */
  async patch<T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    const mappedUrl = getMappedPath(url);
    const response = await this.client.patch<T>(mappedUrl, data, { retries: 2, ...options });
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as Record<string, string>
    };
  }

  /**
   * DELETE request
   */
  async delete<T = any>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    const mappedUrl = getMappedPath(url);
    const response = await this.client.delete<T>(mappedUrl, { retries: 2, ...options });
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as Record<string, string>
    };
  }

  /**
   * Upload file
   */
  async upload<T = any>(url: string, file: File | FormData, options?: RequestOptions): Promise<ApiResponse<T>> {
    const mappedUrl = getMappedPath(url);
    
    let formData: FormData;
    if (file instanceof FormData) {
      formData = file;
    } else {
      formData = new FormData();
      formData.append('file', file);
    }

    const response = await this.client.post<T>(mappedUrl, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      retries: 1,
      ...options
    });

    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as Record<string, string>
    };
  }

  /**
   * Download file
   */
  async download(url: string, filename?: string, options?: RequestOptions): Promise<void> {
    const mappedUrl = getMappedPath(url);
    const response = await this.client.get(mappedUrl, {
      responseType: 'blob',
      ...options
    });

    if (typeof window !== 'undefined') {
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    }
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Load auth token on initialization
if (typeof window !== 'undefined') {
  apiClient.loadAuth();
}

export default apiClient;
