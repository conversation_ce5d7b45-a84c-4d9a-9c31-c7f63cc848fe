/**
 * Validation Utilities
 *
 * Provides validation functions for form fields and entire forms.
 * Supports various validation rules and custom validators.
 */

import type { FormFieldConfig, FormFieldValidation } from "../types";
import { REGEX_PATTERNS, VALIDATION, ERROR_MESSAGES } from "../constants";

// Use patterns from constants
export const VALIDATION_PATTERNS = REGEX_PATTERNS;

// Common validation messages
export const VALIDATION_MESSAGES = {
  required: ERROR_MESSAGES.REQUIRED,
  email: ERROR_MESSAGES.INVALID_EMAIL,
  phone: ERROR_MESSAGES.INVALID_PHONE,
  url: ERROR_MESSAGES.INVALID_URL,
  minLength: (min: number) => `Must be at least ${min} characters long`,
  maxLength: (max: number) => `Must be no more than ${max} characters long`,
  min: (min: number) => `Must be at least ${min}`,
  max: (max: number) => `Must be no more than ${max}`,
  pattern: "Please enter a valid value",
  password: ERROR_MESSAGES.PASSWORD_TOO_WEAK,
};

/**
 * Validate a single field value against its configuration
 * @param {*} value - Field value to validate
 * @param {Object} fieldConfig - Field configuration object
 * @returns {string|null} Error message or null if valid
 */
export function validateField(
  value: any,
  fieldConfig: FormFieldConfig
): string | null {
  const { required, validation = {}, type } = fieldConfig;

  // Convert value to string for validation
  const stringValue = value?.toString() || "";

  // Required validation
  if (required && !stringValue.trim()) {
    return validation.requiredMessage || VALIDATION_MESSAGES.required;
  }

  // Skip other validations if field is empty and not required
  if (!stringValue.trim() && !required) {
    return null;
  }

  // Type-specific validation
  if (type === "email" && !VALIDATION_PATTERNS.EMAIL.test(stringValue)) {
    return validation.emailMessage || VALIDATION_MESSAGES.email;
  }

  if (type === "tel" && !VALIDATION_PATTERNS.PHONE.test(stringValue)) {
    return validation.phoneMessage || VALIDATION_MESSAGES.phone;
  }

  if (type === "url" && !VALIDATION_PATTERNS.URL.test(stringValue)) {
    return validation.urlMessage || VALIDATION_MESSAGES.url;
  }

  // Pattern validation
  if (validation.pattern && !validation.pattern.test(stringValue)) {
    return validation.patternMessage || VALIDATION_MESSAGES.pattern;
  }

  // Length validation
  if (validation.minLength && stringValue.length < validation.minLength) {
    return (
      validation.minLengthMessage ||
      VALIDATION_MESSAGES.minLength(validation.minLength)
    );
  }

  if (validation.maxLength && stringValue.length > validation.maxLength) {
    return (
      validation.maxLengthMessage ||
      VALIDATION_MESSAGES.maxLength(validation.maxLength)
    );
  }

  // Numeric validation
  if (type === "number") {
    const numValue = parseFloat(value);

    if (isNaN(numValue)) {
      return validation.numberMessage || "Please enter a valid number";
    }

    if (validation.min !== undefined && numValue < validation.min) {
      return validation.minMessage || VALIDATION_MESSAGES.min(validation.min);
    }

    if (validation.max !== undefined && numValue > validation.max) {
      return validation.maxMessage || VALIDATION_MESSAGES.max(validation.max);
    }
  }

  // Custom validation function
  if (validation.custom && typeof validation.custom === "function") {
    const customResult = validation.custom(value, fieldConfig);
    if (customResult !== true) {
      return customResult || "Invalid value";
    }
  }

  // Password strength validation
  if (type === "password" && validation.strength !== false) {
    if (!VALIDATION_PATTERNS.PASSWORD_STRONG.test(stringValue)) {
      return validation.passwordMessage || VALIDATION_MESSAGES.password;
    }
  }

  return null;
}

/**
 * Validate an entire form
 * @param {Object} values - Form values object
 * @param {Array} fieldConfigs - Array of field configuration objects
 * @returns {Object} Object with field names as keys and error messages as values
 */
export function validateForm(
  values: Record<string, any>,
  fieldConfigs: FormFieldConfig[]
): Record<string, string> {
  const errors: Record<string, string> = {};

  fieldConfigs.forEach((fieldConfig) => {
    const error = validateField(values[fieldConfig.name], fieldConfig);
    if (error) {
      errors[fieldConfig.name] = error;
    }
  });

  return errors;
}

/**
 * Check if a form is valid
 * @param {Object} values - Form values object
 * @param {Array} fieldConfigs - Array of field configuration objects
 * @returns {boolean} True if form is valid
 */
export function isFormValid(values, fieldConfigs) {
  const errors = validateForm(values, fieldConfigs);
  return Object.keys(errors).length === 0;
}

/**
 * Create a validation schema from field configurations
 * @param {Array} fieldConfigs - Array of field configuration objects
 * @returns {Object} Validation schema object
 */
export function createValidationSchema(fieldConfigs) {
  const schema = {};

  fieldConfigs.forEach((fieldConfig) => {
    schema[fieldConfig.name] = {
      validate: (value) => validateField(value, fieldConfig),
      required: fieldConfig.required || false,
    };
  });

  return schema;
}
