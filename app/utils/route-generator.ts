import { type RouteConfig, route, layout, index } from "@react-router/dev/routes";
import { appConfig, getMappedPath, type PageRouteConfig } from "../config/routes.config";

/**
 * Generates flat routes from the hierarchical configuration
 * This converts nested route configs into React Router v7 flat route structure
 */

interface FlatRoute {
  path: string;
  component: string;
  layout?: string;
  protected: boolean;
  meta?: any;
  index?: boolean;
}

/**
 * Flatten nested route configuration into flat routes
 */
function flattenRoutes(routes: PageRouteConfig[], parentPath = ""): FlatRoute[] {
  const flatRoutes: FlatRoute[] = [];

  for (const routeConfig of routes) {
    const fullPath = parentPath ? `${parentPath}/${routeConfig.path}`.replace(/\/+/g, "/") : routeConfig.path;
    const mappedPath = getMappedPath(fullPath);

    // Add the current route
    flatRoutes.push({
      path: mappedPath,
      component: routeConfig.component,
      layout: routeConfig.layout,
      protected: routeConfig.protected,
      meta: routeConfig.meta,
      index: routeConfig.path === ""
    });

    // Recursively add child routes
    if (routeConfig.children) {
      const childRoutes = flattenRoutes(routeConfig.children, fullPath);
      flatRoutes.push(...childRoutes);
    }
  }

  return flatRoutes;
}

/**
 * Generate React Router v7 route configuration
 */
export function generateRoutes(): RouteConfig {
  const flatRoutes = flattenRoutes(appConfig.pageRoutes);
  const routeConfigs: RouteConfig = [];

  for (const flatRoute of flatRoutes) {
    if (flatRoute.index && flatRoute.path === "/") {
      // Handle index route
      routeConfigs.push(index(flatRoute.component));
    } else if (flatRoute.layout) {
      // Handle layout routes
      const layoutPath = flatRoute.path === "/" ? "" : flatRoute.path;
      
      if (flatRoute.layout === "admin") {
        // Admin layout with nested routes
        const adminRoutes = flatRoutes
          .filter(r => r.path.startsWith(getMappedPath("/admin")) && r.path !== getMappedPath("/admin"))
          .map(r => {
            const relativePath = r.path.replace(getMappedPath("/admin"), "").replace(/^\//, "");
            if (relativePath === "") {
              return index(r.component);
            }
            return route(relativePath, r.component);
          });

        routeConfigs.push(
          route(getMappedPath("/admin"), "admin/layout.tsx", adminRoutes)
        );
      } else {
        // Other layouts
        routeConfigs.push(route(layoutPath, flatRoute.component));
      }
    } else {
      // Regular routes
      if (flatRoute.path !== "/" && !flatRoute.path.startsWith(getMappedPath("/admin"))) {
        routeConfigs.push(route(flatRoute.path, flatRoute.component));
      }
    }
  }

  return routeConfigs;
}

/**
 * Get route metadata for a given path
 */
export function getRouteMeta(path: string): any {
  const flatRoutes = flattenRoutes(appConfig.pageRoutes);
  const route = flatRoutes.find(r => r.path === path);
  return route?.meta || {};
}

/**
 * Check if a route exists in the configuration
 */
export function routeExists(path: string): boolean {
  const flatRoutes = flattenRoutes(appConfig.pageRoutes);
  return flatRoutes.some(r => r.path === path);
}

/**
 * Get all available routes
 */
export function getAllRoutes(): FlatRoute[] {
  return flattenRoutes(appConfig.pageRoutes);
}

/**
 * Generate API route patterns for proxy middleware
 */
export function getApiRoutePatterns(): string[] {
  return appConfig.apiRoutes.map(route => getMappedPath(route.path));
}

/**
 * Get API route configuration by path
 */
export function getApiRouteConfig(path: string) {
  return appConfig.apiRoutes.find(route => {
    const mappedPath = getMappedPath(route.path);
    // Handle parameterized routes
    const pattern = mappedPath.replace(/:([^/]+)/g, "([^/]+)");
    const regex = new RegExp(`^${pattern}$`);
    return regex.test(path);
  });
}

/**
 * Generate breadcrumb data for a given path
 */
export function generateBreadcrumbs(path: string): Array<{ label: string; path: string }> {
  const breadcrumbs: Array<{ label: string; path: string }> = [];
  const pathSegments = path.split("/").filter(Boolean);
  
  let currentPath = "";
  
  for (const segment of pathSegments) {
    currentPath += `/${segment}`;
    const route = getAllRoutes().find(r => r.path === currentPath);
    
    if (route && route.meta?.title) {
      breadcrumbs.push({
        label: route.meta.title,
        path: currentPath
      });
    } else {
      // Fallback to segment name
      breadcrumbs.push({
        label: segment.charAt(0).toUpperCase() + segment.slice(1),
        path: currentPath
      });
    }
  }
  
  return breadcrumbs;
}

/**
 * Get navigation menu structure for admin panel
 */
export function getAdminNavigation() {
  const adminBasePath = getMappedPath("/admin");
  const adminRoutes = getAllRoutes().filter(r => 
    r.path.startsWith(adminBasePath) && 
    r.path !== adminBasePath &&
    !r.path.includes(":") && // Exclude parameterized routes
    r.meta?.title
  );

  const navigation: Array<{
    key: string;
    label: string;
    path: string;
    icon?: string;
    children?: Array<{ key: string; label: string; path: string; }>
  }> = [];

  // Group routes by their first segment after admin
  const grouped: Record<string, typeof adminRoutes> = {};
  
  for (const route of adminRoutes) {
    const relativePath = route.path.replace(adminBasePath, "").replace(/^\//, "");
    const segments = relativePath.split("/");
    const firstSegment = segments[0];
    
    if (!grouped[firstSegment]) {
      grouped[firstSegment] = [];
    }
    grouped[firstSegment].push(route);
  }

  // Convert to navigation structure
  for (const [segment, routes] of Object.entries(grouped)) {
    const mainRoute = routes.find(r => {
      const relativePath = r.path.replace(adminBasePath, "").replace(/^\//, "");
      return relativePath === segment;
    });

    const childRoutes = routes.filter(r => {
      const relativePath = r.path.replace(adminBasePath, "").replace(/^\//, "");
      return relativePath !== segment && relativePath.startsWith(segment + "/");
    });

    if (mainRoute) {
      const navItem = {
        key: segment,
        label: mainRoute.meta?.title || segment,
        path: mainRoute.path,
        children: childRoutes.length > 0 ? childRoutes.map(child => ({
          key: child.path,
          label: child.meta?.title || child.path.split("/").pop() || "",
          path: child.path
        })) : undefined
      };

      navigation.push(navItem);
    }
  }

  return navigation;
}
