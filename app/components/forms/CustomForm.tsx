/**
 * CustomForm Component
 * 
 * A configuration-driven form component that renders dynamic fields and validation
 * from JSON configurations. Supports various field types, validation rules,
 * and extensible field components.
 * 
 * @example
 * const formConfig = {
 *   fields: [
 *     {
 *       name: 'email',
 *       type: 'email',
 *       label: 'Email Address',
 *       required: true,
 *       validation: { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ }
 *     }
 *   ]
 * };
 * 
 * <CustomForm
 *   config={formConfig}
 *   onSubmit={handleSubmit}
 *   initialValues={{ email: '' }}
 * />
 */

import React, { useState, useCallback, useMemo } from 'react';
import { FormField } from './FormField';
import { validateField, validateForm } from '../../utils/validation';

/**
 * Configuration-driven form component
 * @param {Object} props - Component props
 * @param {Object} props.config - Form configuration object
 * @param {Function} props.onSubmit - Form submission handler
 * @param {Object} props.initialValues - Initial form values
 * @param {boolean} props.disabled - Disable entire form
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.fieldComponents - Custom field component overrides
 */
export function CustomForm({
  config,
  onSubmit,
  initialValues = {},
  disabled = false,
  className = '',
  fieldComponents = {},
  ...props
}) {
  // Form state management
  const [values, setValues] = useState(() => {
    const initialState = {};
    config.fields?.forEach(field => {
      initialState[field.name] = initialValues[field.name] || field.defaultValue || '';
    });
    return initialState;
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Memoized field configurations for performance
  const fieldConfigs = useMemo(() => {
    return config.fields?.map(field => ({
      ...field,
      id: field.id || field.name,
      required: field.required || false,
      disabled: disabled || field.disabled || false,
    })) || [];
  }, [config.fields, disabled]);

  /**
   * Handle field value changes
   */
  const handleFieldChange = useCallback((fieldName, value) => {
    setValues(prev => ({ ...prev, [fieldName]: value }));
    
    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({ ...prev, [fieldName]: null }));
    }
  }, [errors]);

  /**
   * Handle field blur events
   */
  const handleFieldBlur = useCallback((fieldName) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    
    // Validate field on blur
    const field = fieldConfigs.find(f => f.name === fieldName);
    if (field) {
      const error = validateField(values[fieldName], field);
      setErrors(prev => ({ ...prev, [fieldName]: error }));
    }
  }, [fieldConfigs, values]);

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(async (event) => {
    event.preventDefault();
    
    if (isSubmitting) return;

    setIsSubmitting(true);

    try {
      // Validate all fields
      const formErrors = validateForm(values, fieldConfigs);
      
      if (Object.keys(formErrors).length > 0) {
        setErrors(formErrors);
        setTouched(
          fieldConfigs.reduce((acc, field) => {
            acc[field.name] = true;
            return acc;
          }, {})
        );
        return;
      }

      // Clear errors and submit
      setErrors({});
      await onSubmit(values);

    } catch (error) {
      console.error('Form submission error:', error);
      // Handle submission errors (could set form-level error state)
    } finally {
      setIsSubmitting(false);
    }
  }, [values, fieldConfigs, onSubmit, isSubmitting]);

  /**
   * Reset form to initial state
   */
  const resetForm = useCallback(() => {
    const resetValues = {};
    fieldConfigs.forEach(field => {
      resetValues[field.name] = initialValues[field.name] || field.defaultValue || '';
    });
    setValues(resetValues);
    setErrors({});
    setTouched({});
  }, [fieldConfigs, initialValues]);

  // Form validation state
  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0 && 
           fieldConfigs.every(field => 
             !field.required || (values[field.name] && values[field.name].toString().trim())
           );
  }, [errors, fieldConfigs, values]);

  return (
    <form
      onSubmit={handleSubmit}
      className={`custom-form ${className}`}
      noValidate
      {...props}
    >
      {/* Form title and description */}
      {config.title && (
        <div className="form-header mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {config.title}
          </h2>
          {config.description && (
            <p className="text-gray-600">
              {config.description}
            </p>
          )}
        </div>
      )}

      {/* Form fields */}
      <div className="form-fields space-y-4">
        {fieldConfigs.map((field) => (
          <FormField
            key={field.id}
            config={field}
            value={values[field.name]}
            error={touched[field.name] ? errors[field.name] : null}
            onChange={(value) => handleFieldChange(field.name, value)}
            onBlur={() => handleFieldBlur(field.name)}
            customComponents={fieldComponents}
          />
        ))}
      </div>

      {/* Form actions */}
      <div className="form-actions mt-6 flex gap-3">
        {config.showResetButton !== false && (
          <button
            type="button"
            onClick={resetForm}
            disabled={disabled || isSubmitting}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {config.resetButtonText || 'Reset'}
          </button>
        )}
        
        <button
          type="submit"
          disabled={disabled || isSubmitting || !isValid}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          {isSubmitting && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          )}
          {config.submitButtonText || 'Submit'}
        </button>
      </div>

      {/* Form-level error display */}
      {config.showFormErrors !== false && Object.keys(errors).length > 0 && (
        <div className="form-errors mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-800 text-sm font-medium">
            Please correct the following errors:
          </p>
          <ul className="mt-1 text-red-700 text-sm list-disc list-inside">
            {Object.entries(errors).map(([field, error]) => (
              error && <li key={field}>{error}</li>
            ))}
          </ul>
        </div>
      )}
    </form>
  );
}
