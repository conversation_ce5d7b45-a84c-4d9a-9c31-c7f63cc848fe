import { type RouteConfig, route, index } from "@react-router/dev/routes";
import { getMappedPath } from "./config/routes.config";

/**
 * Dynamic route configuration using flat route structure
 * Routes are generated from the configuration file and support dynamic path mapping
 */
export default [
  // Home/Landing page
  index("routes/home.tsx"),

  // Admin routes with dynamic path mapping
  route(getMappedPath("/admin"), "routes/admin/layout.tsx", [
    // Admin dashboard (index route)
    index("routes/admin/dashboard.tsx"),

    // User management routes
    route("users", "routes/admin/users/layout.tsx", [
      index("routes/admin/users/list.tsx"),
      route("create", "routes/admin/users/create.tsx"),
      route(":id", "routes/admin/users/detail.tsx"),
      route(":id/edit", "routes/admin/users/edit.tsx"),
    ]),

    // Settings routes
    route("settings", "routes/admin/settings/layout.tsx", [
      index("routes/admin/settings/general.tsx"),
      route("security", "routes/admin/settings/security.tsx"),
      route("integrations", "routes/admin/settings/integrations.tsx"),
    ]),

    // Reports routes
    route("reports", "routes/admin/reports/layout.tsx", [
      index("routes/admin/reports/overview.tsx"),
      route("analytics", "routes/admin/reports/analytics.tsx"),
      route("exports", "routes/admin/reports/exports.tsx"),
    ]),
  ]),
] satisfies RouteConfig;
