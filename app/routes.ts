import { type RouteConfig, index, route } from "@react-router/dev/routes";
import { ROUTES } from "./config/routes";

// Generate routes dynamically from configuration
const routeConfigs: RouteConfig = [
  // Index route (home page)
  index("pages/home.tsx"),

  // Dynamic routes from configuration
  route(ROUTES.LOGIN.path, "pages/auth/login.tsx"),
  route(ROUTES.REGISTER.path, "pages/auth/register.tsx"),
  // route(ROUTES.FORGOT_PASSWORD.path, "pages/auth/forgot-password.tsx"),
  // route(ROUTES.DASHBOARD.path, "pages/dashboard/overview.tsx"),
  route(ROUTES.ADMIN_DASHBOARD.path, "pages/admin/dashboard.tsx"),
  // route(ROUTES.USER_MANAGEMENT.path, "pages/admin/users.tsx"),
  // route(ROUTES.SETTINGS.path, "pages/settings/general.tsx"),
  // route(ROUTES.PROFILE.path, "pages/profile/edit.tsx"),
  // route(ROUTES.NOT_FOUND.path, "pages/errors/not-found.tsx"),
  // route(ROUTES.UNAUTHORIZED.path, "pages/errors/unauthorized.tsx"),
];

export default routeConfigs;
