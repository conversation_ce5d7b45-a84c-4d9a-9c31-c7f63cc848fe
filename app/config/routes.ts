/**
 * Centralized Route Configuration
 *
 * This file contains all route definitions for the application.
 * Routes can be obfuscated and changed at any time by updating this configuration.
 * No hardcoded routes should exist anywhere else in the application.
 *
 * Security Features:
 * - Obfuscated route paths
 * - Dynamic route generation
 * - Centralized route management
 * - Runtime route updates (optional)
 *
 * @example
 * import { ROUTES, getRoutePath } from '../config/routes';
 *
 * // Get obfuscated path
 * const adminPath = getRoutePath('ADMIN_DASHBOARD');
 *
 * // Navigate using route key
 * navigate(ROUTES.ADMIN_DASHBOARD.path);
 */

// Type definitions
export interface RouteMetadata {
  title?: string;
  description?: string;
  keywords?: string;
  canonical?: string;
  robots?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  twitterCard?: string;
}

export interface RouteConfig {
  key: string;
  path: string;
  name: string;
  public: boolean;
  component?: string;
  roles?: string[];
  parent?: string;
  type?: "page" | "api";
  meta?: RouteMetadata;
}

export interface RoutesConfig {
  [key: string]: RouteConfig;
}

// Route configuration object
// Paths can be obfuscated and changed without affecting the application logic
export const ROUTES: RoutesConfig = {
  // Public routes
  HOME: {
    key: "HOME",
    path: "/",
    name: "Home",
    public: true,
    component: "pages/home",
    meta: {
      title: "Augment - Home",
      description: "Welcome to Augment application",
    },
  },

  LOGIN: {
    key: "LOGIN",
    path: "/auth/signin-portalkkkkkkk",
    name: "Login",
    public: true,
    component: "pages/auth/login",
    meta: {
      title: "Login - Augment",
      description: "Sign in to your account",
    },
  },

  REGISTER: {
    key: "REGISTER",
    path: "/auth/create-account-gateway",
    name: "Register",
    public: true,
    component: "pages/auth/register",
    meta: {
      title: "Register - Augment",
      description: "Create a new account",
    },
  },

  FORGOT_PASSWORD: {
    key: "FORGOT_PASSWORD",
    path: "/auth/recovery-portal-xyz",
    name: "Forgot Password",
    public: true,
    component: "pages/auth/forgot-password",
    meta: {
      title: "Password Recovery - Augment",
    },
  },

  // Protected routes (obfuscated paths)
  DASHBOARD: {
    key: "DASHBOARD",
    path: "/workspace/overview-hub-v2",
    name: "Dashboard",
    public: false,
    component: "pages/dashboard/overview",
    meta: {
      title: "Dashboard - Augment",
      description: "Your workspace overview",
    },
  },

  ADMIN_DASHBOARD: {
    key: "ADMIN_DASHBOARD",
    path: "/control-center-qwerty-admin-xyz123",
    name: "Admin Dashboard",
    public: false,
    roles: ["admin", "super_admin"],
    component: "pages/admin/dashboard",
    meta: {
      title: "Admin Dashboard - Augment",
      description: "Administrative control panel",
    },
  },

  USER_MANAGEMENT: {
    key: "USER_MANAGEMENT",
    path: "/control-center-qwerty-admin-xyz123/user-registry-mgmt",
    name: "User Management",
    public: false,
    roles: ["admin", "super_admin"],
    component: "pages/admin/users",
    parent: "ADMIN_DASHBOARD",
    meta: {
      title: "User Management - Augment",
    },
  },

  SETTINGS: {
    key: "SETTINGS",
    path: "/workspace/configuration-panel-v3",
    name: "Settings",
    public: false,
    component: "pages/settings/general",
    meta: {
      title: "Settings - Augment",
    },
  },

  PROFILE: {
    key: "PROFILE",
    path: "/workspace/user-profile-mgmt",
    name: "Profile",
    public: false,
    component: "pages/profile/edit",
    meta: {
      title: "Profile - Augment",
    },
  },

  // API routes (for reference, not used in frontend routing)
  API_BASE: {
    key: "API_BASE",
    path: "/api/v1",
    name: "API Base",
    type: "api",
  },

  // Error routes
  NOT_FOUND: {
    key: "NOT_FOUND",
    path: "/404",
    name: "Not Found",
    public: true,
    component: "pages/errors/not-found",
    meta: {
      title: "404 - Page Not Found",
    },
  },

  UNAUTHORIZED: {
    key: "UNAUTHORIZED",
    path: "/unauthorized",
    name: "Unauthorized",
    public: true,
    component: "pages/errors/unauthorized",
    meta: {
      title: "401 - Unauthorized",
    },
  },
};

// Create a reverse lookup map for quick path-to-route resolution
export const PATH_TO_ROUTE: Record<string, RouteConfig> = Object.values(
  ROUTES
).reduce((acc, route) => {
  acc[route.path] = route;
  return acc;
}, {} as Record<string, RouteConfig>);

/**
 * Get route path by route key
 * @param {string} routeKey - Route key (e.g., 'ADMIN_DASHBOARD')
 * @returns {string} Route path
 */
export function getRoutePath(routeKey: string): string {
  const route = ROUTES[routeKey];
  if (!route) {
    console.warn(`Route key "${routeKey}" not found`);
    return "/";
  }
  return route.path;
}

/**
 * Get route configuration by key
 * @param {string} routeKey - Route key
 * @returns {Object|null} Route configuration object
 */
export function getRoute(routeKey: string): RouteConfig | null {
  return ROUTES[routeKey] || null;
}

/**
 * Get route configuration by path
 * @param {string} path - Route path
 * @returns {Object|null} Route configuration object
 */
export function getRouteByPath(path: string): RouteConfig | null {
  return PATH_TO_ROUTE[path] || null;
}

/**
 * Get all public routes
 * @returns {Array} Array of public route configurations
 */
export function getPublicRoutes(): RouteConfig[] {
  return Object.values(ROUTES).filter((route) => route.public);
}

/**
 * Get all protected routes
 * @returns {Array} Array of protected route configurations
 */
export function getProtectedRoutes(): RouteConfig[] {
  return Object.values(ROUTES).filter(
    (route) => !route.public && route.type !== "api"
  );
}

/**
 * Get routes accessible by user role
 * @param {Array} userRoles - User roles array
 * @returns {Array} Array of accessible route configurations
 */
export function getRoutesByRole(userRoles = []) {
  return Object.values(ROUTES).filter((route) => {
    if (route.public) return true;
    if (!route.roles) return true; // No role restriction
    return route.roles.some((role) => userRoles.includes(role));
  });
}

/**
 * Check if user can access a route
 * @param {string} routeKey - Route key
 * @param {Array} userRoles - User roles array
 * @param {boolean} isAuthenticated - User authentication status
 * @returns {boolean} True if user can access the route
 */
export function canAccessRoute(
  routeKey,
  userRoles = [],
  isAuthenticated = false
) {
  const route = ROUTES[routeKey];
  if (!route) return false;

  // Public routes are always accessible
  if (route.public) return true;

  // Protected routes require authentication
  if (!isAuthenticated) return false;

  // Check role-based access
  if (route.roles && route.roles.length > 0) {
    return route.roles.some((role) => userRoles.includes(role));
  }

  return true;
}

/**
 * Generate navigation menu items based on user permissions
 * @param {Array} userRoles - User roles array
 * @param {boolean} isAuthenticated - User authentication status
 * @returns {Array} Array of navigation menu items
 */
export function generateNavigation(userRoles = [], isAuthenticated = false) {
  const accessibleRoutes = Object.values(ROUTES).filter((route) => {
    // Skip API routes and error routes
    if (
      route.type === "api" ||
      route.key.includes("ERROR") ||
      route.key === "NOT_FOUND"
    ) {
      return false;
    }

    // Skip auth routes if user is authenticated
    if (
      isAuthenticated &&
      ["LOGIN", "REGISTER", "FORGOT_PASSWORD"].includes(route.key)
    ) {
      return false;
    }

    // Skip protected routes if user is not authenticated
    if (!isAuthenticated && !route.public) {
      return false;
    }

    return canAccessRoute(route.key, userRoles, isAuthenticated);
  });

  return accessibleRoutes.map((route) => ({
    key: route.key,
    path: route.path,
    name: route.name,
    parent: route.parent,
    meta: route.meta,
  }));
}

// Export route keys as constants for type safety
export const ROUTE_KEYS = Object.keys(ROUTES).reduce((acc, key) => {
  acc[key] = key;
  return acc;
}, {});
