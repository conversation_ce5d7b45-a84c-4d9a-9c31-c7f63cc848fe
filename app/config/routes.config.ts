/**
 * Dynamic Route Configuration System
 * This config allows changing route paths dynamically and manages both frontend routes and API proxy routes
 */

export interface RouteMapping {
  /** Original route path */
  original: string;
  /** Mapped/obfuscated route path */
  mapped: string;
  /** Whether this route is active */
  active: boolean;
}

export interface ApiRouteConfig {
  /** Route path pattern */
  path: string;
  /** HTTP methods allowed */
  methods: ("GET" | "POST" | "PUT" | "PATCH" | "DELETE")[];
  /** Target backend URL (if proxying) */
  target?: string;
  /** Whether to proxy to external API */
  proxy: boolean;
  /** Mock response for development */
  mockResponse?: any;
  /** Rate limiting config */
  rateLimit?: {
    windowMs: number;
    max: number;
  };
}

export interface PageRouteConfig {
  /** Route path */
  path: string;
  /** Component file path relative to routes directory */
  component: string;
  /** Layout to use */
  layout?: string;
  /** Whether route requires authentication */
  protected: boolean;
  /** Route metadata */
  meta?: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
  /** Child routes */
  children?: PageRouteConfig[];
}

export interface AppConfig {
  /** Base path for admin routes */
  adminBasePath: string;
  /** Route mappings for obfuscation */
  routeMappings: RouteMapping[];
  /** API route configurations */
  apiRoutes: ApiRouteConfig[];
  /** Page route configurations */
  pageRoutes: PageRouteConfig[];
  /** Proxy configuration */
  proxy: {
    /** Default target for API proxying */
    defaultTarget: string;
    /** Timeout for proxy requests */
    timeout: number;
    /** Whether to change origin */
    changeOrigin: boolean;
  };
}

/**
 * Main application configuration
 * This can be modified to change route paths, add new routes, or configure proxy settings
 */
export const appConfig: AppConfig = {
  adminBasePath: "/admin",

  routeMappings: [
    {
      original: "/admin",
      mapped: "/adminxxxx", // Change this to obfuscate: "/asewenermHFWs"
      active: true,
    },
    {
      original: "/api",
      mapped: "/api", // Change this to obfuscate: "/api-v2-secure"
      active: true,
    },
  ],

  apiRoutes: [
    // User management APIs
    {
      path: "/api/users",
      methods: ["GET", "POST"],
      proxy: true,
      target: "http://localhost:3001",
    },
    {
      path: "/api/users/:id",
      methods: ["GET", "PUT", "PATCH", "DELETE"],
      proxy: true,
      target: "http://localhost:3001",
    },

    // Dashboard APIs
    {
      path: "/api/dashboard/stats",
      methods: ["GET"],
      proxy: false,
      mockResponse: {
        totalUsers: 1250,
        activeUsers: 890,
        revenue: 45000,
        growth: 12.5,
      },
    },

    // Settings APIs
    {
      path: "/api/settings",
      methods: ["GET", "PUT"],
      proxy: true,
      target: "http://localhost:3001",
    },

    // File upload API
    {
      path: "/api/upload",
      methods: ["POST"],
      proxy: true,
      target: "http://localhost:3001",
      rateLimit: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 10, // limit each IP to 10 requests per windowMs
      },
    },
  ],

  pageRoutes: [
    {
      path: "/",
      component: "home.tsx",
      layout: "public",
      protected: false,
      meta: {
        title: "Admin Panel",
        description: "Secure admin panel for application management",
      },
    },
    {
      path: "/admin",
      component: "admin/layout.tsx",
      layout: "admin",
      protected: true,
      children: [
        {
          path: "",
          component: "admin/dashboard.tsx",
          protected: true,
          meta: {
            title: "Dashboard",
            description: "Admin dashboard with key metrics",
          },
        },
        {
          path: "users",
          component: "admin/users/layout.tsx",
          protected: true,
          children: [
            {
              path: "",
              component: "admin/users/list.tsx",
              protected: true,
              meta: {
                title: "Users",
                description: "User management",
              },
            },
            {
              path: "create",
              component: "admin/users/create.tsx",
              protected: true,
              meta: {
                title: "Create User",
                description: "Create new user",
              },
            },
            {
              path: ":id",
              component: "admin/users/detail.tsx",
              protected: true,
              meta: {
                title: "User Details",
                description: "View and edit user details",
              },
            },
            {
              path: ":id/edit",
              component: "admin/users/edit.tsx",
              protected: true,
              meta: {
                title: "Edit User",
                description: "Edit user information",
              },
            },
          ],
        },
        {
          path: "settings",
          component: "admin/settings/layout.tsx",
          protected: true,
          children: [
            {
              path: "",
              component: "admin/settings/general.tsx",
              protected: true,
              meta: {
                title: "General Settings",
                description: "General application settings",
              },
            },
            {
              path: "security",
              component: "admin/settings/security.tsx",
              protected: true,
              meta: {
                title: "Security Settings",
                description: "Security and authentication settings",
              },
            },
            {
              path: "integrations",
              component: "admin/settings/integrations.tsx",
              protected: true,
              meta: {
                title: "Integrations",
                description: "Third-party integrations",
              },
            },
          ],
        },
        {
          path: "reports",
          component: "admin/reports/layout.tsx",
          protected: true,
          children: [
            {
              path: "",
              component: "admin/reports/overview.tsx",
              protected: true,
              meta: {
                title: "Reports Overview",
                description: "Reports and analytics overview",
              },
            },
            {
              path: "analytics",
              component: "admin/reports/analytics.tsx",
              protected: true,
              meta: {
                title: "Analytics",
                description: "Detailed analytics and metrics",
              },
            },
            {
              path: "exports",
              component: "admin/reports/exports.tsx",
              protected: true,
              meta: {
                title: "Data Exports",
                description: "Export data and reports",
              },
            },
          ],
        },
      ],
    },
  ],

  proxy: {
    defaultTarget: "http://localhost:3001",
    timeout: 30000,
    changeOrigin: true,
  },
};

/**
 * Get the mapped path for a given original path
 */
export function getMappedPath(originalPath: string): string {
  const mapping = appConfig.routeMappings.find(
    (m) => m.active && originalPath.startsWith(m.original)
  );

  if (mapping) {
    return originalPath.replace(mapping.original, mapping.mapped);
  }

  return originalPath;
}

/**
 * Get the original path for a given mapped path
 */
export function getOriginalPath(mappedPath: string): string {
  const mapping = appConfig.routeMappings.find(
    (m) => m.active && mappedPath.startsWith(m.mapped)
  );

  if (mapping) {
    return mappedPath.replace(mapping.mapped, mapping.original);
  }

  return mappedPath;
}

/**
 * Check if a route is protected
 */
export function isProtectedRoute(path: string): boolean {
  const findRoute = (
    routes: PageRouteConfig[],
    currentPath: string
  ): boolean => {
    for (const route of routes) {
      if (
        route.path === currentPath ||
        currentPath.startsWith(route.path + "/")
      ) {
        if (route.protected) return true;
        if (route.children) {
          const childPath = currentPath
            .replace(route.path, "")
            .replace(/^\//, "");
          return findRoute(route.children, childPath);
        }
      }
    }
    return false;
  };

  return findRoute(appConfig.pageRoutes, path);
}
